<template>
  <div class="worktype-auto-complete">
    <AutoComplete
      v-model:value="inputValue"
      category="worktype"
      :placeholder="placeholder"
      :disabled="disabled"
      :allowClear="allowClear"
      :size="size"
      :limit="limit"
      :searchType="searchType"
      :allowAutoCreate="allowAutoCreate"
      :showHelpChar="showHelpChar"
      :showUseCount="showUseCount"
      :dropdownMatchSelectWidth="dropdownMatchSelectWidth"
      :getPopupContainer="getPopupContainer"
      :showPopularOnFocus="showPopularOnFocus"
      :autoUpdateUseCount="autoUpdateUseCount"
      @select="handleSelect"
      @create="handleCreate"
      @change="handleChange"
      @focus="handleFocus"
      @blur="handleBlur"
      ref="autoCompleteRef"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, computed, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import AutoComplete from './AutoComplete.vue';
import type { WorktypeResult } from './WorktypeAutoComplete.api';

interface OptionItem {
  value: string;
  label: string;
  helpChar?: string;
  useCount?: number;
  isNew?: boolean;
  matchType?: string;
  raw?: WorktypeResult;
}

const props = defineProps({
  value: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请输入工种名称或助记码'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  allowClear: {
    type: Boolean,
    default: true
  },
  size: {
    type: String,
    default: 'middle'
  },
  limit: {
    type: Number,
    default: 10
  },
  searchType: {
    type: String as () => 'name' | 'helpChar' | 'both',
    default: 'both'
  },
  allowAutoCreate: {
    type: Boolean,
    default: true
  },
  showHelpChar: {
    type: Boolean,
    default: true
  },
  showUseCount: {
    type: Boolean,
    default: true
  },
  dropdownMatchSelectWidth: {
    type: Boolean,
    default: true
  },
  getPopupContainer: {
    type: Function,
    default: undefined
  },
  showPopularOnFocus: {
    type: Boolean,
    default: true
  },
  autoUpdateUseCount: {
    type: Boolean,
    default: true
  },
  // 工种特有的配置
  validateWorktype: {
    type: Boolean,
    default: false
  },
  // 工种验证规则
  worktypeRules: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:value', 'change', 'select', 'create', 'focus', 'blur']);

const inputValue = ref(props.value);
const autoCompleteRef = ref();

// 监听外部value变化
watch(() => props.value, (newVal) => {
  inputValue.value = newVal;
});

// 监听内部value变化
watch(inputValue, (newVal) => {
  emit('update:value', newVal);
});

// 处理选择
const handleSelect = (value: string, option: OptionItem) => {
  // 确保选择的值立即同步到 inputValue
  inputValue.value = value;

  // 工种特有的处理逻辑
  if (props.validateWorktype) {
    if (!validateWorktypeValue(value)) {
      message.warning('请选择有效的工种');
      return;
    }
  }

  emit('select', value, option);
};

// 处理创建
const handleCreate = (newItem: any) => {
  message.success(`工种"${newItem.name}"创建成功`);
  emit('create', newItem);
};

// 处理值变化
const handleChange = (value: string) => {
  emit('change', value);
};

// 处理获得焦点
const handleFocus = () => {
  emit('focus');
};

// 处理失去焦点 - 延迟触发以避免与 select 事件的竞态条件
const handleBlur = () => {
  // 延迟触发 blur 事件，确保 select 事件先完成
  // 这样可以避免表单验证时获取到旧值的问题
  setTimeout(() => {
    emit('blur');
  }, 100);
};

// 工种验证
const validateWorktypeValue = (value: string): boolean => {
  if (!value || !value.trim()) {
    return false;
  }
  
  // 基本验证规则
  if (value.length > 50) {
    message.warning('工种名称不能超过50个字符');
    return false;
  }
  
  // 自定义验证规则
  for (const rule of props.worktypeRules) {
    if (typeof rule === 'function') {
      const result = rule(value);
      if (result !== true) {
        message.warning(result || '工种验证失败');
        return false;
      }
    }
  }
  
  return true;
};

// 获取工种建议
const getWorktypeSuggestions = () => {
  return [
    '电工', '焊工', '司机', '操作工', '维修工',
    '清洁工', '管理员', '技术员', '质检员', '安全员',
    '机械工', '装配工', '包装工', '搬运工', '保安',
    '厨师', '服务员', '销售员', '会计', '文员'
  ];
};

// 暴露方法给父组件
defineExpose({
  // 通用方法
  loadPopularOptions: () => autoCompleteRef.value?.loadPopularOptions(),
  clearOptions: () => autoCompleteRef.value?.clearOptions(),
  refresh: () => autoCompleteRef.value?.refresh(),
  
  // 工种特有方法
  validateWorktype: validateWorktypeValue,
  getWorktypeSuggestions,
  
  // 获取当前值
  getValue: () => inputValue.value,
  
  // 设置值
  setValue: (value: string) => {
    inputValue.value = value;
  },
  
  // 清空值
  clear: () => {
    inputValue.value = '';
  },
  
  // 获取焦点
  focus: () => {
    autoCompleteRef.value?.$el?.querySelector('input')?.focus();
  },
  
  // 失去焦点
  blur: () => {
    autoCompleteRef.value?.$el?.querySelector('input')?.blur();
  }
});
</script>

<style lang="less" scoped>
.worktype-auto-complete {
  width: 100%;
  
  // 工种特有的样式定制
  :deep(.ant-select-dropdown) {
    .option-item {
      .help-char {
        background-color: #e6f7ff;
        color: #1890ff;
        border: 1px solid #91d5ff;
      }
      
      .new-tag {
        background-color: #52c41a;
        color: white;
      }
    }
  }
  
  // 工种输入框特有样式
  :deep(.ant-input) {
    &:focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .worktype-auto-complete {
    :deep(.option-item) {
      .help-char {
        display: none; // 小屏幕隐藏助记码
      }
    }
  }
}
</style>
